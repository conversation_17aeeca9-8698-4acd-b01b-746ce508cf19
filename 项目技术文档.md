# Eternal UniApp 项目技术文档

## 项目概述

本项目基于 **unibest** 框架构建，是一个现代化的 UniApp 开发模板，支持多端开发（H5、小程序、App）。

### 技术栈

- **框架**: UniApp + Vue 3 + TypeScript
- **构建工具**: Vite 5
- **样式**: UnoCSS + SCSS
- **UI 组件库**: wot-design-uni
- **状态管理**: Pinia + pinia-plugin-persistedstate
- **HTTP 请求**: 
  - 简单版本 HTTP (src/http/http.ts)
  - Alova (src/http/alova.ts) 
  - Vue Query (src/http/vue-query.ts)
- **分页组件**: z-paging
- **日期处理**: dayjs
- **Cookie 管理**: js-cookie
- **代码规范**: ESLint + Prettier + Husky

### 平台兼容性

| H5  | iOS | Android | 微信小程序 | 字节小程序 | 快手小程序 | 支付宝小程序 | 钉钉小程序 | 百度小程序 |
| --- | --- | ------- | ---------- | ---------- | ---------- | ------------ | ---------- | ---------- |
| ✅   | ✅   | ✅       | ✅          | ✅          | ✅          | ✅            | ✅          | ✅          |

## 环境要求

- Node.js >= 22
- pnpm = 10.10.0
- Vue Official >= 2.1.10
- TypeScript >= 5.0

## 项目结构

```
src/
├── api/                    # API 接口定义
├── components/             # 公共组件
├── hooks/                  # 自定义 hooks
├── http/                   # HTTP 请求封装
├── layouts/                # 布局组件
├── pages/                  # 主包页面
├── pages-sub/              # 分包页面
├── router/                 # 路由配置
├── service/                # 业务服务层
├── static/                 # 静态资源
├── store/                  # 状态管理
├── style/                  # 全局样式
├── tabbar/                 # 底部导航
├── types/                  # 类型定义
├── utils/                  # 工具函数
├── App.vue                 # 应用入口
├── main.ts                 # 主入口文件
├── pages.json              # 页面配置
└── uni.scss                # 全局样式变量
```

## 开发指南

### 1. 项目启动

```bash
# 安装依赖
pnpm install

# 开发环境
pnpm dev          # H5 开发
pnpm dev:mp       # 微信小程序开发
pnpm dev:app      # App 开发

# 生产构建
pnpm build        # H5 构建
pnpm build:mp     # 微信小程序构建
pnpm build:app    # App 构建
```

### 2. HTTP 请求使用

项目提供三种 HTTP 请求方式：

#### 简单版本 HTTP
```typescript
// src/api/foo.ts
import { http } from '@/http/http'

export const getUserInfo = (id: string) => {
  return http.get(`/user/${id}`)
}
```

#### Alova 请求
```typescript
// src/api/foo-alova.ts
import { alova } from '@/http/alova'

export const getUserInfo = (id: string) => {
  return alova.Get(`/user/${id}`)
}
```

#### Vue Query 请求
```typescript
// src/service/user.vuequery.ts
import { useQuery } from '@tanstack/vue-query'

export const useUserInfo = (id: string) => {
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => getUserInfo(id)
  })
}
```

### 3. 路由配置

#### 页面路由定义
在 Vue 文件中使用 `<route>` 块定义路由：

```vue
<!-- 首页设置 -->
<route lang="jsonc" type="home">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "首页"
  }
}
</route>

<!-- 普通页面 -->
<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "页面标题"
  },
  "layout": "default"
}
</route>
```

#### 路由跳转
```typescript
// 页面跳转
uni.navigateTo({
  url: '/pages/about/about'
})

// 切换 Tab
uni.switchTab({
  url: '/pages/index/index'
})

// 重定向
uni.redirectTo({
  url: '/pages/login/login'
})
```

### 4. 新页面创建

#### 主包页面
1. 在 `src/pages/` 目录下创建页面文件夹
2. 创建 `index.vue` 文件
3. 使用 `<route>` 块配置页面信息

```vue
<!-- src/pages/example/index.vue -->
<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "示例页面"
  }
}
</route>

<template>
  <view class="container">
    <text>示例页面内容</text>
  </view>
</template>

<script setup lang="ts">
defineOptions({
  name: 'ExamplePage'
})

// 页面逻辑
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
}
</style>
```

#### 分包页面
1. 在 `src/pages-sub/` 目录下创建分包文件夹
2. 创建页面文件
3. 页面会自动注册到分包配置中

### 5. 分包配置

项目支持自动分包，配置在 `vite.config.ts` 中：

```typescript
UniPages({
  exclude: ['**/components/**/**.*'],
  subPackages: ['src/pages-sub'], // 分包目录
  dts: 'src/types/uni-pages.d.ts',
})
```

分包页面路径示例：
- 主包：`/pages/index/index`
- 分包：`/pages-sub/demo/index`

### 6. 组件使用

#### 自动导入组件
项目配置了组件自动导入：

```typescript
// vite.config.ts
Components({
  extensions: ['vue'],
  deep: true,
  directoryAsNamespace: false,
  dts: 'src/types/components.d.ts',
})
```

#### 组件命名规范
- 自定义组件：`fg-*` 前缀，如 `fg-button`
- wot-design-uni：`wd-*` 前缀，如 `wd-button`
- z-paging：`z-paging*` 前缀

### 7. 样式开发

#### UnoCSS 使用
```vue
<template>
  <!-- 原子化 CSS -->
  <view class="flex justify-center items-center p-4 bg-primary text-white">
    内容
  </view>
  
  <!-- 自定义快捷类 -->
  <view class="center">
    居中内容
  </view>
</template>
```

#### SCSS 变量
```scss
// 使用全局 SCSS 变量
.custom-style {
  color: $uni-color-primary;
  font-size: $uni-font-size-base;
}
```

### 8. 状态管理

#### Store 定义
```typescript
// src/store/user.ts
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    token: ''
  }),
  
  actions: {
    setUserInfo(info: any) {
      this.userInfo = info
    }
  },
  
  persist: true // 持久化存储
})
```

#### Store 使用
```vue
<script setup>
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

// 使用状态
console.log(userStore.userInfo)

// 调用方法
userStore.setUserInfo({ name: '用户' })
</script>
```

### 9. 登录拦截配置

项目支持两种登录策略：

#### 默认无需登录策略 (DEFAULT_NO_NEED_LOGIN)
- 大部分页面无需登录
- 黑名单页面需要登录

#### 默认需要登录策略 (DEFAULT_NEED_LOGIN)  
- 大部分页面需要登录
- 白名单页面无需登录

配置文件：`src/router/config.ts`

### 10. 环境配置

环境变量文件位于 `env/` 目录：
- `.env.development` - 开发环境
- `.env.production` - 生产环境
- `.env.test` - 测试环境

## 常用命令

```bash
# 开发
pnpm dev:h5              # H5 开发
pnpm dev:mp              # 微信小程序开发
pnpm dev:app             # App 开发

# 构建
pnpm build:h5            # H5 构建
pnpm build:mp            # 微信小程序构建
pnpm build:app           # App 构建

# 代码检查
pnpm lint                # ESLint 检查
pnpm lint:fix            # ESLint 修复

# 类型检查
pnpm type-check          # TypeScript 类型检查
```

## 注意事项

1. **包管理器**：必须使用 pnpm，不支持 npm 或 yarn
2. **Node 版本**：需要 Node.js >= 22
3. **编辑器**：推荐使用 VSCode + Vue Official 插件
4. **小程序开发**：需要对应平台的开发者工具
5. **App 开发**：需要 HBuilderX 进行真机调试和打包

## 更多资源

- [unibest 官方文档](https://unibest.tech/)
- [UniApp 官方文档](https://uniapp.dcloud.net.cn/)
- [Vue 3 官方文档](https://cn.vuejs.org/)
- [UnoCSS 文档](https://unocss.dev/)
- [wot-design-uni 文档](https://wot-design-uni.pages.dev/)
